//##### 도메인 체크 #####//
var host		= location.host.replace(".com","").replace(".co.kr","").replace(".net","").split(".");
var domain		= host[host.length-1];
var domainCate	= (domain=='mgame')?'ghost':((domain=='cartoonnetworkkorea')?'cartoon':domain);
//##### 각게임 js 파일 가져오기 #####//
//var gName		= host[0].replace("mg","").replace("alpha-","").replace('hon','ghost');
var gName		= host[0].replace("mg","").replace("alpha-","");
var Nowtime		= new Date().getTime();

function $_GET(name){
    var param	= location.search;
    var getVal	= '';
    if(param!=''){
        var paramArr	= param.slice(1,param.length).split('&');
        for(i=0; i<paramArr.length; i++){
            if(paramArr[i].split('=')[0]==name){
                getVal = paramArr[i].split('=')[1];
                break;
            }
        }
    }
    return getVal;
}
document.write('<script type="text/javascript" src="https://m-static.mgame.com/js/mzzang.v7.js"></script>');
document.write('<script type="text/javascript" src="https://hon.mgame.com/news/launch/mzzang.cycle.js"></script>');
document.write('<script type="text/javascript" src="https://m-static.mgame.com/common_js/write_embed_v2.js"></script>');

var IMG		= "https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/";
var arrCate	= new Array('notice','event','update');							// 공지탭
var arrIcon	= new Array();			// 공지 카테고리 분류 아이콘
arrIcon['N'] = 'notice.jpg';
arrIcon['E'] = 'event.jpg';
arrIcon['U'] = 'update.jpg';
var ad_txt	= '';
var notice_txt	= '';
var HotItem_txt = '';

$(document).ready(function(){
    //############################### 광고영역 이미지 & 동영상 ########################################//
    if(ad_mode=='image'){
        for(i=0; i<ad_image.length; i++){
            ad_txt += '<a href="'+ad_link[i]+'" target="_blank"><img src="'+ad_image[i]+'" width="300" height="225" style="cursor:pointer;" /></a>';
        }
        $("#slideList").html( ad_txt );

        $('#slideList').before('<div id="sliderPage">').cycle({
            fx:     'fade',
            speed:  'slow',
            timeout: 5000,
            pager:  '#sliderPage'
        });
    }
    //########################################################################################//

    //############################## 메인공지와 같은 공지 내용 출력 (전체에 대해 따로 없어서 공지2 업데이트1 이벤트2 출력) 테스트 서버 런처 공지 구분 ##########################//
    if($_GET("rtype")=='test'){
        $.post('testZoneNotice.mgame',{},function(rs){
            $("#LaunchNotice").html( rs );
        });
    }else{
        notice_txt		+=	'<table width="354" border="0" cellspacing="3" cellpadding="0" class="noticeList">';
        for(i=0; i<3; i++){
            var tmpVal = eval('admin_'+domainCate+'_'+arrCate[i]).split('||');
            var allCnt = 8;
            if(i==2) allCnt = 4;
            for(j=1; j<tmpVal[0]*4; j+=4){
                if(j>allCnt) break;
                notice_txt	+=	'	<tr>';
                notice_txt	+=	'		<td width="268" class="a_l"><img src="'+IMG+arrIcon[tmpVal[j+3]]+'" width="11" height="10" /> ';
                notice_txt	+=	'		<a class="f_c" onClick="viewLayer('+tmpVal[j+1]+',this)">'+tmpVal[j]+'</a></td>';
                notice_txt	+=	'		<td width="80" class="f_c a_r">'+tmpVal[j+2].substr(0,10)+'</td>';
                notice_txt	+=	'	</tr>';
            }
        }
        notice_txt	+=	'	</table>';

        for(i = 0; i < 2; i ++){		// 업데이트는 출력안하기때문에
            notice_txt		+=	'<table width="354" border="0" cellspacing="3" cellpadding="0" class="noticeList">';
            var tmpVal = eval('admin_'+domainCate+'_'+arrCate[i]).split('||');
            for(j=1; j<tmpVal[0]*4; j+=4){
                if(j>20) break;	// 내용이 5개이상이면
                notice_txt	+=	'	<tr>';
                notice_txt	+=	'		<td width="268" class="a_l"><img src="'+IMG+arrIcon[tmpVal[j+3]]+'" width="11" height="10" /> ';
                notice_txt	+=	'		<a class="f_c" onClick="viewLayer('+tmpVal[j+1]+',this)">'+tmpVal[j]+'</a></td>';
                notice_txt	+=	'		<td width="80" class="f_c a_r">'+tmpVal[j+2].substr(0,10)+'</td>';
                notice_txt	+=	'	</tr>';
            }
            notice_txt	+=	'	</table>';
        }
        $("#LaunchNotice").html( notice_txt );
        noticeView(0);
    }
    //#######################################################################################//

    //################################## Hot Item List ########################################//
    //내용 작성
    HotItem_txt = '';
    HotItem_txt += '<ul>';
    for(i=0; i<hotItem_img.length; i++){
        HotItem_txt += '	<li>';
        if(hotItem_img[i]){
            HotItem_txt += '	<table border="0" cellspacing="0" cellpadding="0">';
            HotItem_txt += '		<tr>';
            HotItem_txt += '			<td><a href="'+hotItem_link[i]+'" target="_blank"><img src="'+hotItem_img[i]+'" width="62" height="62"/></a></td>';
            HotItem_txt += '			<td width="10">&nbsp;</td>';
            HotItem_txt += '			<td>';
            HotItem_txt += '				<table width="168" border="0" cellspacing="0" cellpadding="0">';
            HotItem_txt += '					<tr>';
            HotItem_txt += '						<td class="f_c f_w">'+hotItem_name[i]+'</td>';
            HotItem_txt += '					</tr>';
            HotItem_txt += '					<tr>';
            HotItem_txt += '						<td class="f_c">'+hotItem_txt[i]+'</td>';
            HotItem_txt += '					</tr>';
            HotItem_txt += '				</table>';
            HotItem_txt += '			</td>';
            HotItem_txt += '		</tr>';
            HotItem_txt += '	</table>';
        }else{
            HotItem_txt += '	<a href="'+hotItem_link[i]+'" target="_blank"><img src="'+hotItem_img[i]+'" height="62" width="240" /></a>';
        }
        HotItem_txt += '	</li>';
    }
    HotItem_txt += '</ul>';
    $('#HotItemSlide').html( HotItem_txt );
    //HotItem Slide Start
    defaults();
    $('#BtnNext').click(function(){	slider('next',true);	});
    $('#BtnPre').click(function(){	slider('pre',true);	});
    //######################################################################################//

    $('a,area').focus(function(){	$(this).blur();	});

});

function keyF5(){
    var keyValue = event.keyCode;
    if( keyValue == 116 ) {	//F5 IE
        event.keyCode = "";
        return false;
    }
    return true;
}
document.onkeydown= keyF5;

//###################################### Notice Tabs ############################################//
function noticeView(index){
    $(".imgover").each(function(i){
        $(".imgover").eq(i).attr('src', $(".imgover").eq(i).attr('src').replace('_on',''));
    });
    if($(".imgover").eq(index).attr('src').indexOf("_on") < 0 ){
        var src = $(".imgover").eq(index).attr('src');
        $(".imgover").eq(index).attr('src', src.replace('.jpg','_on.jpg'));
    }
    $(".noticeList").css('display','none');
    $(".noticeList").eq(index).show();
}

var layer_l;
var layer_t;
//############################# Notice View Layer ######################################//
function viewLayer(idx,ob,rtype){
    var type	= rtype ? rtype : '';
    layer_l = $(ob).offset().left;
    layer_t = $(ob).offset().top;
    $('#viewLayer').css({left:layer_l, top:layer_t});
    $('#viewLayer').animate({left:0, top:0, width:742, height:438}, 400 , function(){
        $('#lodingImg').show();
        $.post(location.protocol + "//" + location.host + '/news/launch/launch_view.mgame',{idx:idx},function(rs){
            $('#lodingImg').hide();
            $('#viewLayer').html(rs).show();
        });
    });
}
function close(){
    $('#viewLayer').animate({left:layer_l, top:layer_t, width:0, height:0}, 300 );
    $("#viewLayer").empty().hide();
}

//################################## Hot Item List ########################################//
var t_num = 0;
var w = 0;
var h = 0;
var len = 0;
var timeout;

function defaults(){
    w = $('#HotItemSlide li').width();
    h = $('#HotItemSlide li').height();
    len = $('#HotItemSlide li').length;
    $('#HotItemSlide').width(w);
    $('#HotItemSlide').height(h);
    $("ul", '#HotItemSlide').css('float','left');
    slider("start",false);
}

function slider(mode,clicked){
    switch(mode){
        case 'next':
            t_num = (t_num>=len-1) ? 0 : t_num+1;
            break;
        case 'pre':
            t_num = (t_num<=0) ? len-1 : t_num-1;
            break;
    }
    p = (t_num*w*-1);
    T = (t_num*h*-1);
    $('#HotItemSlide ul').animate({marginTop:T}, 400);

    if(clicked) clearTimeout(timeout);
    timeout = setTimeout(function(){ slider("next",false); }, 5000);
}
//################################## Hot Item List ########################################//

function openPop(opts){
    window.open(opts.url,opts.title,opts.option);
}