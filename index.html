<script type="text/javascript">
	function repVariable(param){
		var repArray			=	[["http:\/\/hon.mgame.com","https:\/\/hon.mgame.com"],["http:\/\/mghon.mgame.com","https:\/\/hon.mgame.com"],["http:\/\/image.mgame.com","https:\/\/mgameimage.gscdn.com"],["http:\/\/mgfile.mgame.com","https:\/\/mgamemgfile.gscdn.com"],["http:\/\/file1.mgame.com","https:\/\/mgamefile1.gscdn.com"],["http:\/\/hon.hangame.com","https:\/\/hon.hangame.com"]];
		var regexAllCase		=	'';

		if(param){
			if(param.constructor != Array){
				for(i = 0; i < repArray.length; i ++){
					regexAllCase=	new RegExp(repArray[i][0], "gi");
					param		=	param.replace(regexAllCase, repArray[i][1]);
				}
				return param;
			}
			else{
				var reparraysalt=	'@#$#%~!^^&^@*$^';

				for(j = 0; j < param.length; j ++){
					param[j]	=	param[j] + reparraysalt;
				}

				param			=	param.toString();

				for(i = 0; i < repArray.length; i ++){
					regexAllCase=	new RegExp(repArray[i][0], "gi");
					param		=	param.replace(regexAllCase, repArray[i][1]);
				}

				param			=	param.split(reparraysalt + ',');

				for(x = 0; x < param.length; x ++){
					param[x]	=	param[x].replace(reparraysalt, "");
				}
				return param;
			}
		}
	}
</script><!doctype html>
<html>

<!-- Mirrored from hon.mgame.com/news/launch/launch.mgame by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 13 Jun 2025 06:38:31 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=euc-kr" /><!-- /Added by HTTrack -->
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=euc-kr">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<style type="text/css">
		*{margin:0; padding:0; font:11px/14px dotum;}
		ul, ol, li{list-style:none;}
		a{color:#625445; text-decoration:none; cursor:pointer;}
		img{border:0; border:none;}
		fieldset.launcher{width:742px; height:438px; padding:0 ; border:none 0; background:url(images/bg.jpg) no-repeat ; color:#dddddd; overflow:hidden;}
		.ulnotice{clear:both; margin:155px 23px 148px 39px;}
		.ulnotice li{float:left; margin:0 0 0 0px; display:inline;}
		.f_c {color: #e6dad4}
		.a_l {text-align:left;}
		.a_r {text-align:right;}
		.a_c {text-align:center;}
		.f_w {font-weight: bold;}
		.f_c1 {color: #E4F09E}
		.f_c2 {color: #efefef}
		.imgover{cursor:pointer}
		.limited-text {
			max-width: 200px;       /* Giới hạn chiều rộng */
			white-space: nowrap;    /* Không xuống dòng */
			overflow: hidden;       /* Ẩn phần vượt quá */
			text-overflow: ellipsis;/* Hiển thị "..." nếu bị cắt */
			border: 1px solid #ccc; /* (tuỳ chọn) để dễ thấy khung */
		}
		/* AD_Image */
		#slideList {margin-left:0px; margin-top:-3px; position:relative;}
		#sliderPage {position:relative; top:210px; text-align:right; z-index:99; width:300px;}
		#sliderPage a{margin: 0 2px; padding:3px; background:#6f2a69; text-decoration:none; font-size:11px; color:#eaeae9; font-weight:bold; font-family:Verdana; border:1px solid #a3459a;}
		#sliderPage a.activeSlide { background:#3b0b37; color:#f4ffa9; border:1px solid #64165d; }
		#sliderPage a:focus { outline: none; }
		/* hot item */
		#HotItemSlide {position:relative; height:62px; overflow:hidden; float:left; margin-top:5px;}
		/* notice view layer */
		#viewLayer{position:absolute; display:none; z-index:991; background-color:#fff;}
		/* notice view content */
		#view_txt{position:relative; padding:10px; border-bottom:2px solid #fff; height:368px; _height:388px; _width:620px; overflow-y:auto; overflow-x:hidden; color:#b9aec9; background-color:#fff;}
	</style>
	<script type="text/javascript" src="m-static.mgame.com/js/mzzang.v7.js"></script>

	<script type="text/javascript" src="m-static.mgame.com/common_gamejs/hon_new_notice2852.js?ver=1749796713"></script>
	<script type="text/javascript" src="m-static.mgame.com/common_gamejs/hon_new_launch_hot2852.js?ver=1749796713"></script>
	<script type="text/javascript" src="m-static.mgame.com/js/launch.js?ver=1749796713"></script>
	<script type="text/javascript">
		if(typeof ad_image != 'undefined'){ ad_image = repVariable(ad_image); }
		if(typeof ad_link != 'undefined'){ ad_link = repVariable(ad_link); }
		if(typeof hotItem_img != 'undefined'){ hotItem_img = repVariable(hotItem_img); }
		if(typeof hotItem_link != 'undefined'){ hotItem_link = repVariable(hotItem_link); }
		if(typeof hotItem_txt != 'undefined'){ hotItem_txt = repVariable(hotItem_txt); }
	</script>
</head>
<body oncontextmenu="return false" onselectstart="return false" ondragstart="return false" style="overflow:hidden;" bgProperties=fixed>
<fieldset class="launcher">
	<div id="lodingImg" style="position:absolute; display:none; top:220px; left:380px; z-index:992;"><img src="mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/ajaxloding.gif"></div>
	<div id="viewLayer"></div>
	<div style="margin-left:462px; margin-top:22px;">
		<p><a href="https://csweb.mgame.com/helpdesk/faq/faq_search.mgame?QUESTION=hon" target="_blank"><img src="mgameimage.gscdn.com/mgamezzang/games/hon_2007/common/empty.gif" width="77" height="17"></a><a href="https://hon.mgame.com/community/?rtype=B" target="_blank"><img src="mgameimage.gscdn.com/mgamezzang/games/hon_2007/common/empty.gif" width="84" height="17"></a><a href="https://hon.mgame.com/info/?rtype=G" target="_blank"><img src="mgameimage.gscdn.com/mgamezzang/games/hon_2007/common/empty.gif" width="90" height="17"/></a></p>
	</div>
	<ul class="ulnotice">
		<li><div id="slideList"></div></li>
		<li>
			<table width="380" height="225" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td width="380" height="138">
						<table width="380" height="138" border="0" cellspacing="0" cellpadding="0">
							<tr>
								<td height="34"><img src="images/notice_btn01.png" width="128" height="34" class="imgover" onclick="noticeView(0);"></td>
								<td><img src="images/notice_btn02.png" width="121" height="34" class="imgover" onclick="noticeView(1);"></td>
								<td><img src="images/notice_btn03.png" width="131" height="34" class="imgover" onclick="noticeView(2);"></td>
							</tr>
							<tr>
								<td height="104" class="limited-text" colspan="3" align="center" valign="middle" id="LaunchNotice">
									<table width="354" border="0" cellspacing="3" cellpadding="0" class="noticeList" style="">
										<tbody>
											<tr>
												<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6241,this)">[안내] 6월 11일 서버점검 및 1차 업데이트 안내</a></td>
												<td width="80" class="f_c a_r">2025-06-10</td>
											</tr>
											<tr>
												<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6240,this)">[안내] 6월 6일 행복달력 이벤트 안내</a></td>
												<td width="80" class="f_c a_r">2025-06-05</td>
											</tr>
											<tr>
												<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/event.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6239,this)">귀혼 6월 행복달력 ♥</a
												></td>
												<td width="80" class="f_c a_r">2025-06-04</td>
											</tr>
											<tr>
												<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/update.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6236,this)">[안내] 5월 28일 업데이트 안내 (수정)</a></td>
												<td width="80" class="f_c a_r">2025-05-27</td>
											</tr>
										</tbody>
									</table>
									<table width="354" border="0" cellspacing="3" cellpadding="0" class="noticeList" style="display: none;">	<tbody><tr>		<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6241,this)">[안내] 6월 11일 서버점검 및 1차 업데이트 안내</a></td>		<td width="80" class="f_c a_r">2025-06-10</td>	</tr>	<tr>		<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6240,this)">[안내] 6월 6일 행복달력 이벤트 안내</a></td>		<td width="80" class="f_c a_r">2025-06-05</td>	</tr>	<tr>		<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6238,this)">[완료] 5월 28일 임시 점검 안내 (수정)</a></td>		<td width="80" class="f_c a_r">2025-05-28</td>	</tr>	<tr>		<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6237,this)">[안내] 5월 캐릭터명 신청 안내</a></td>		<td width="80" class="f_c a_r">2025-05-28</td>	</tr>	<tr>		<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/notice.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6235,this)">[완료] 5월 28일 서버점검 및 2차 업데이트 안내</a></td>		<td width="80" class="f_c a_r">2025-05-27</td>	</tr>	</tbody></table><table width="354" border="0" cellspacing="3" cellpadding="0" class="noticeList" style="display: none;">	<tbody><tr>		<td width="268" class="limited-text" class="a_l"><img src="https://mgameimage.gscdn.com/mgamezzang/games/hon_v1/launch/event.jpg" width="11" height="10"> 		<a class="f_c" onclick="viewLayer(6239,this)">귀혼 6월 행복달력 ♥</a></td>		<td width="80" class="f_c a_r">2025-06-04</td>	</tr>	</tbody></table></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<th>
						<table width="380" height="87" border="0" cellspacing="0" cellpadding="0">
							<tr>
								<td>
									<div style="margin-top:10px;">
										<ul style="margin-left:20px;">
											<li><a href="#" id="BtnPre"><img src="images/trai.png" width="50" height="62"></a></li>
										</ul>
										<div id="HotItemSlide" style="width: 240px; height: 62px"></div>
										<ul>
											<li><a href="#" id="BtnNext"><img src="images/phai.png" width="50" height="62"></a></li>
										</ul>
									</div>
									<!-- Hot Item -->
								</td>
							</tr>
						</table>
					</th>
				</tr>
			</table>
		</li>
	</ul>
</fieldset>
</body>

<!-- Mirrored from hon.mgame.com/news/launch/launch.mgame by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 13 Jun 2025 06:38:37 GMT -->
</html>